import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import store from './redux/store';
import { <PERSON>rowserRouter as Router } from 'react-router-dom';
import App from './App';
import { AuthProvider } from './contexts/AuthContext';
import { AnalyticsProvider } from './components/analytics';
import { ThemeProvider } from './components/theme/ThemeManager';
import SimpleErrorBoundary from './components/SimpleErrorBoundary';
import { register as registerServiceWorker } from './serviceWorkerRegistration';
import { disableMockWebSocketServer } from './utils/mockWebSocketServer';

// Import CSS files
import './index.css';
import './App.css';
import 'antd/dist/reset.css';

// ENHANCED REACT GLOBAL EXPOSURE - Fix timing and consistency issues
function exposeReactGlobals() {
  // Immediate exposure
  window.React = React;
  window.ReactDOM = { createRoot };

  // Enhanced global exposure for testing and debugging
  if (typeof window !== 'undefined') {
    // Safely set React DevTools hook - check if it's already defined and writable
    try {
      if (!window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = {};
      }
    } catch (error) {
      // React DevTools is already installed and the property is read-only
      console.log('React DevTools already installed, skipping hook setup');
    }

    window.__REACT_VERSION__ = React.version;
    window.__REACT_LOADED__ = true;
    window.__REACT_GLOBALS_EXPOSED__ = true;

    // Ensure globals are accessible and locked
    Object.defineProperty(window, 'React', {
      value: React,
      writable: false,
      configurable: false,
      enumerable: true
    });

    Object.defineProperty(window, 'ReactDOM', {
      value: { createRoot },
      writable: false,
      configurable: false,
      enumerable: true
    });

    // Expose to parent window if in iframe
    try {
      if (window.parent && window.parent !== window) {
        window.parent.React = React;
        window.parent.ReactDOM = { createRoot };
        window.parent.__REACT_LOADED__ = true;
      }
    } catch (e) {
      // Cross-origin iframe, ignore
    }

    // Expose to top window if nested
    try {
      if (window.top && window.top !== window) {
        window.top.React = React;
        window.top.ReactDOM = { createRoot };
        window.top.__REACT_LOADED__ = true;
      }
    } catch (e) {
      // Cross-origin frame, ignore
    }
  }
}

// Call the exposure function immediately
exposeReactGlobals();

// Immediate verification
console.log('🚀 React loaded successfully:', React.version);
console.log('🔧 React available globally:', typeof window.React);
console.log('🏗️ ReactDOM createRoot available:', typeof createRoot);
console.log('✅ React globals exposed and verified');

// Additional verification after DOM ready
document.addEventListener('DOMContentLoaded', () => {
  console.log('🔍 DOM Ready - React globals check:', {
    React: typeof window.React,
    ReactDOM: typeof window.ReactDOM,
    version: window.__REACT_VERSION__,
    loaded: window.__REACT_LOADED__
  });
});

// Simple CSS for basic styling
const styles = `
  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    box-sizing: border-box;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// Add styles to document
const styleSheet = document.createElement('style');
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);

// Get root element
const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found');
}

// Create React root
const root = createRoot(rootElement);

// Enhanced App component with full functionality
const EnhancedApp = () => {
  return (
    <Provider store={store}>
      <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <AnalyticsProvider>
          <AuthProvider>
            <SimpleErrorBoundary>
              <ThemeProvider initialTheme="light">
                <App />
              </ThemeProvider>
            </SimpleErrorBoundary>
          </AuthProvider>
        </AnalyticsProvider>
      </Router>
    </Provider>
  );
};

// FORCE DISABLE MOCK WEBSOCKET - Multiple aggressive checks
console.log('🔌 FORCING REAL WEBSOCKET MODE - Disabling all mock WebSocket functionality...');

// Check if mock WebSocket is active and disable it
if (window.WebSocket !== window._originalWebSocket && window._originalWebSocket) {
  console.log('🔌 Mock WebSocket detected - restoring real WebSocket...');
  disableMockWebSocketServer();
}

// Additional check - if WebSocket class has mock characteristics, restore native
if (window.WebSocket && window.WebSocket.name === 'MockWebSocket') {
  console.log('🔌 MockWebSocket class detected - forcing native WebSocket...');
  if (window._originalWebSocket) {
    window.WebSocket = window._originalWebSocket;
  }
}

// Set global flags to ensure real API usage
window.USE_REAL_API = true;
window.MOCK_SERVERS_ENABLED = false;
window.FORCE_REAL_WEBSOCKET = true;

// Log current WebSocket class for debugging
console.log('🔌 Current WebSocket class:', window.WebSocket?.name || 'WebSocket');
console.log('🔌 Real API mode:', window.USE_REAL_API);
console.log('🔌 Mock servers disabled:', !window.MOCK_SERVERS_ENABLED);

// Enhanced app loading state management
function setAppLoadingState(loading, loaded = false) {
  window.__APP_LOADING__ = loading;
  window.__APP_LOADED__ = loaded;

  // Broadcast to all frames
  try {
    if (window.parent && window.parent !== window) {
      window.parent.__APP_LOADING__ = loading;
      window.parent.__APP_LOADED__ = loaded;
    }
    if (window.top && window.top !== window) {
      window.top.__APP_LOADING__ = loading;
      window.top.__APP_LOADED__ = loaded;
    }
  } catch (e) {
    // Cross-origin frames, ignore
  }

  console.log(`📊 App state: loading=${loading}, loaded=${loaded}`);
}

// Diagnostic widget for development
function addDiagnosticWidget() {
  if (document.getElementById('react-diagnostic-widget')) {
    console.log('🔧 Diagnostic widget already exists');
    return; // Already added
  }

  console.log('🔧 Creating diagnostic widget...');

  const widget = document.createElement('div');
  widget.id = 'react-diagnostic-widget';
  widget.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #3b82f6;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 12px;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
    user-select: none;
  `;

  function updateWidget() {
    try {
      const reactOk = typeof window.React !== 'undefined';
      const appOk = window.__APP_LOADED__ && !window.__APP_LOADING__;
      const rootOk = document.getElementById('root')?.innerHTML?.length > 0;

      const allOk = reactOk && appOk && rootOk;

      widget.style.background = allOk ? '#10b981' : '#ef4444';
      widget.textContent = `🔍 React ${allOk ? '✅' : '❌'}`;
      widget.title = `React: ${reactOk ? '✅' : '❌'} | App: ${appOk ? '✅' : '❌'} | Root: ${rootOk ? '✅' : '❌'}`;
    } catch (error) {
      console.error('Error updating diagnostic widget:', error);
    }
  }

  widget.onclick = () => {
    // Show in-app diagnostic panel
    showDiagnosticPanel();
  };

  // Ensure DOM is ready before adding widget
  if (document.body) {
    updateWidget();
    document.body.appendChild(widget);
    console.log('✅ Diagnostic widget added to DOM');
  } else {
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', () => {
      updateWidget();
      document.body.appendChild(widget);
      console.log('✅ Diagnostic widget added to DOM (after DOMContentLoaded)');
    });
  }

  // Update widget every 2 seconds
  setInterval(updateWidget, 2000);

  console.log('🔍 Diagnostic widget configured - click to view status');
}

// In-app diagnostic panel
function showDiagnosticPanel() {
  // Remove existing panel if present
  const existingPanel = document.getElementById('diagnostic-panel');
  if (existingPanel) {
    existingPanel.remove();
    return;
  }

  const panel = document.createElement('div');
  panel.id = 'diagnostic-panel';
  panel.style.cssText = `
    position: fixed;
    top: 20px;
    left: 20px;
    width: 400px;
    max-height: 80vh;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 10001;
    overflow: hidden;
    border: 2px solid #3b82f6;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;

  const header = document.createElement('div');
  header.style.cssText = `
    background: #3b82f6;
    color: white;
    padding: 12px 16px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
  `;
  header.innerHTML = `
    <span>🔍 React Diagnostics</span>
    <button onclick="document.getElementById('diagnostic-panel').remove()" style="
      background: none;
      border: none;
      color: white;
      font-size: 18px;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
    ">×</button>
  `;

  const content = document.createElement('div');
  content.style.cssText = `
    padding: 16px;
    max-height: 60vh;
    overflow-y: auto;
  `;

  // Generate diagnostic content
  const diagnostics = {
    timestamp: new Date().toLocaleTimeString(),
    react: {
      available: typeof window.React !== 'undefined',
      version: window.React?.version || null,
      reactDOM: typeof window.ReactDOM !== 'undefined',
      globalsExposed: window.__REACT_GLOBALS_EXPOSED__ || false
    },
    app: {
      loaded: window.__APP_LOADED__,
      loading: window.__APP_LOADING__,
      reactLoaded: window.__REACT_LOADED__
    },
    dom: {
      rootExists: !!document.getElementById('root'),
      rootHasContent: document.getElementById('root')?.innerHTML?.length > 0,
      rootContentLength: document.getElementById('root')?.innerHTML?.length || 0
    },
    css: {
      linksCount: document.querySelectorAll('link[rel="stylesheet"]').length,
      files: Array.from(document.querySelectorAll('link[rel="stylesheet"]')).map(link => link.href.split('/').pop())
    }
  };

  const statusStyle = (success) => `
    padding: 8px 12px;
    border-radius: 4px;
    margin: 4px 0;
    font-weight: 500;
    background: ${success ? '#d1fae5' : '#fee2e2'};
    color: ${success ? '#065f46' : '#991b1b'};
  `;

  content.innerHTML = `
    <div style="font-size: 12px; color: #6b7280; margin-bottom: 12px;">
      Last updated: ${diagnostics.timestamp}
    </div>

    <h3 style="margin: 0 0 8px 0; color: #374151;">⚛️ React Status</h3>
    <div style="${statusStyle(diagnostics.react.available)}">
      ${diagnostics.react.available ? '✅' : '❌'} React: ${diagnostics.react.available ? diagnostics.react.version : 'Not Available'}
    </div>
    <div style="${statusStyle(diagnostics.react.reactDOM)}">
      ${diagnostics.react.reactDOM ? '✅' : '❌'} ReactDOM: ${diagnostics.react.reactDOM ? 'Available' : 'Not Available'}
    </div>
    <div style="${statusStyle(diagnostics.react.globalsExposed)}">
      ${diagnostics.react.globalsExposed ? '✅' : '❌'} Globals Exposed: ${diagnostics.react.globalsExposed}
    </div>

    <h3 style="margin: 16px 0 8px 0; color: #374151;">📊 App State</h3>
    <div style="${statusStyle(diagnostics.app.loaded)}">
      ${diagnostics.app.loaded ? '✅' : '❌'} App Loaded: ${diagnostics.app.loaded}
    </div>
    <div style="${statusStyle(!diagnostics.app.loading)}">
      ${!diagnostics.app.loading ? '✅' : '❌'} App Loading: ${diagnostics.app.loading} (should be false)
    </div>
    <div style="${statusStyle(diagnostics.app.reactLoaded)}">
      ${diagnostics.app.reactLoaded ? '✅' : '❌'} React Loaded Flag: ${diagnostics.app.reactLoaded}
    </div>

    <h3 style="margin: 16px 0 8px 0; color: #374151;">🏗️ DOM Structure</h3>
    <div style="${statusStyle(diagnostics.dom.rootExists)}">
      ${diagnostics.dom.rootExists ? '✅' : '❌'} Root Element: ${diagnostics.dom.rootExists ? 'Found' : 'Not Found'}
    </div>
    <div style="${statusStyle(diagnostics.dom.rootHasContent)}">
      ${diagnostics.dom.rootHasContent ? '✅' : '❌'} Root Content: ${diagnostics.dom.rootHasContent ? `${diagnostics.dom.rootContentLength} chars` : 'Empty'}
    </div>

    <h3 style="margin: 16px 0 8px 0; color: #374151;">🎨 CSS & Resources</h3>
    <div style="${statusStyle(diagnostics.css.linksCount > 0)}">
      ${diagnostics.css.linksCount > 0 ? '✅' : '❌'} CSS Links: ${diagnostics.css.linksCount}
    </div>
    ${diagnostics.css.files.length > 0 ? `
      <div style="padding: 8px 12px; background: #dbeafe; color: #1e40af; border-radius: 4px; margin: 4px 0; font-size: 12px;">
        📄 Files: ${diagnostics.css.files.join(', ')}
      </div>
    ` : ''}
  `;

  panel.appendChild(header);
  panel.appendChild(content);
  document.body.appendChild(panel);

  console.group('🔍 React Diagnostics');
  console.log('Timestamp:', diagnostics.timestamp);
  console.log('React:', diagnostics.react);
  console.log('App State:', diagnostics.app);
  console.log('DOM:', diagnostics.dom);
  console.log('CSS:', diagnostics.css);
  console.groupEnd();
}

// Render the app with enhanced state tracking
try {
  console.log('🚀 Starting App Builder 201...');
  console.log('🔌 Using real WebSocket connections to backend');

  // Set initial loading state
  setAppLoadingState(true, false);

  // Render with callback to track completion
  root.render(
    <React.StrictMode>
      <EnhancedApp />
    </React.StrictMode>
  );

  // Use setTimeout to ensure render is complete
  setTimeout(() => {
    setAppLoadingState(false, true);

    console.log('✅ App Builder 201 loaded successfully!');
    console.log('🔍 Final React global check:', {
      React: typeof window.React,
      ReactDOM: typeof window.ReactDOM,
      version: window.__REACT_VERSION__,
      loaded: window.__REACT_LOADED__,
      appLoaded: window.__APP_LOADED__,
      appLoading: window.__APP_LOADING__
    });
  }, 100);

  // Register service worker for offline support and caching
  registerServiceWorker({
    onSuccess: (registration) => {
      console.log('✅ Service Worker registered successfully:', registration);
    },
    onUpdate: (registration) => {
      console.log('🔄 Service Worker updated:', registration);
      // You could show a notification to the user here
    },
    onWaiting: () => {
      console.log('⏳ Service Worker waiting for activation');
    }
  });

  // Add diagnostic widget for development
  const isDevelopment = process.env.NODE_ENV === 'development' ||
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.port === '3000';

  console.log(`🔧 Environment check: NODE_ENV=${process.env.NODE_ENV}, hostname=${window.location.hostname}, port=${window.location.port}, isDevelopment=${isDevelopment}`);

  // Always add diagnostic widget in development environments
  if (isDevelopment) {
    setTimeout(() => {
      console.log('🔧 Adding diagnostic widget...');
      addDiagnosticWidget();
    }, 2000);
  } else {
    console.log('🔧 Diagnostic widget disabled in production');
  }
} catch (error) {
  console.error('❌ Failed to load App Builder 201:', error);

  // Fallback UI
  root.render(
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      fontFamily: 'Arial, sans-serif',
      background: '#f8fafc',
      color: '#1f2937',
      textAlign: 'center',
      padding: '2rem'
    }}>
      <div style={{
        background: 'white',
        padding: '2rem',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        maxWidth: '500px'
      }}>
        <h1 style={{ margin: '0 0 1rem', color: '#dc2626' }}>App Loading Error</h1>
        <p style={{ margin: '0 0 1rem' }}>
          There was an error loading the App Builder application.
        </p>
        <button
          onClick={() => window.location.reload()}
          style={{
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          Reload Page
        </button>
      </div>
    </div>
  );
}
